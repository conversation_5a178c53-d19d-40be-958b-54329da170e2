import jwt from 'jsonwebtoken';
import userModel from '../models/user.models.js';

export const authUser = async (req,res,next) =>{
    try{
        const token = req.headers.authorization?.split(' ')[1] || req.cookies.token;

        if(!token){
            return res.status(401).json({
                error: 'Unauthorized - No token provided'
            });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Fetch the complete user data from database
        const user = await userModel.findById(decoded._id);
        if (!user) {
            return res.status(401).json({
                error: 'Unauthorized - User not found'
            });
        }

        req.user = user;
        next();

    }
    catch(err){
        console.log('Auth error:', err.message);
        res.status(401).json({
            error: 'Unauthorized - Invalid token'
        });
    }
}