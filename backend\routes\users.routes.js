import { Router } from 'express';
import * as userController from '../controllers/user.controller.js';
import { body } from 'express-validator';
import * as authMiddlware from '../middlewares/auth.middleware.js';

const router = Router();


router.post('/register',

    [body('email').isEmail().withMessage('Email is not valid')
        , body('password').isLength({ min: 3 }).withMessage('Password must be at least 6 characters')],
    userController.createUserController);

router.post('/login',
        [body('email').isEmail().withMessage('Email is not valid')
        , body('password').isLength({ min: 3 }).withMessage('Password must be at least 6 characters')],
    userController.loginController);

router.get('/profile',authMiddlware.authUser,userController.profileController);

export default router;